.dashboard-container {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
  background: #f8fafc;
  min-height: 100vh;

  .page-header {
    margin-bottom: 2rem;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 1rem;

      .header-text {
        .page-title {
          font-size: 2rem;
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 0.25rem 0;
        }

        .page-subtitle {
          font-size: 0.875rem;
          color: #6b7280;
          margin: 0;
        }
      }

      .header-actions {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex-wrap: wrap;

        .period-dropdown {
          min-width: 120px;
        }

        .download-btn {
          white-space: nowrap;
        }

        .date-range {
          font-size: 0.875rem;
          color: #6b7280;
          white-space: nowrap;
        }
      }
    }
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;

    .stat-card {
      border: none;
      box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
      border-radius: 12px;

      .stat-content {
        display: flex;
        align-items: center;
        gap: 1rem;

        .stat-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 48px;
          height: 48px;
          border-radius: 12px;
          background: #f8fafc;

          i {
            font-size: 1.5rem;
          }
        }

        .stat-details {
          flex: 1;

          .stat-value {
            font-size: 1.75rem;
            font-weight: 700;
            color: #1f2937;
            margin: 0 0 0.25rem 0;
          }

          .stat-label {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0 0 0.5rem 0;
          }

          .stat-trend {
            .trend-value {
              font-size: 0.75rem;
              font-weight: 600;
              padding: 0.25rem 0.5rem;
              border-radius: 4px;

              &.positive {
                color: #059669;
                background: #d1fae5;
              }

              &.negative {
                color: #dc2626;
                background: #fee2e2;
              }
            }
          }
        }
      }
    }
  }

  .charts-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;

    .chart-card {
      border: none;
      box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
      border-radius: 12px;

      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;

        h3 {
          font-size: 1.125rem;
          font-weight: 600;
          color: #1f2937;
          margin: 0;
        }

        .chart-legend {
          display: flex;
          gap: 1rem;
          flex-wrap: wrap;

          .legend-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.75rem;
            color: #6b7280;

            .legend-dot {
              width: 8px;
              height: 8px;
              border-radius: 50%;
            }

            &.personal .legend-dot {
              background: #6366f1;
            }

            &.corporate .legend-dot {
              background: #8b5cf6;
            }

            &.investment .legend-dot {
              background: #06b6d4;
            }
          }
        }
      }
    }

    .wallet-card {
      border: none;
      box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
      border-radius: 12px;

      .wallet-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;

        h3 {
          font-size: 1.125rem;
          font-weight: 600;
          color: #1f2937;
          margin: 0;
        }
      }

      .wallet-content {
        .wallet-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.75rem 0;
          border-bottom: 1px solid #f3f4f6;

          &:last-child {
            border-bottom: none;
          }

          .wallet-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;

            .wallet-currency {
              font-weight: 600;
              color: #1f2937;
            }

            .wallet-percentage {
              font-size: 0.875rem;
              color: #6b7280;
            }
          }

          .wallet-amount {
            font-weight: 600;
            color: #1f2937;
          }
        }
      }
    }
  }

  .transactions-card {
    border: none;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border-radius: 12px;

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;

      h3 {
        font-size: 1.125rem;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
      }
    }

    .transactions-table {
      .user-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;

        .user-avatar {
          width: 32px;
          height: 32px;
          font-size: 0.75rem;
          font-weight: 600;
        }

        .user-name {
          font-weight: 500;
          color: #1f2937;
        }
      }

      .coin-info {
        .coin-icon {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;
          border-radius: 50%;
          color: white;
          font-size: 0.75rem;
          font-weight: bold;
        }
      }

      .amount {
        font-weight: 600;
        color: #1f2937;
      }
    }
  }
}

// Responsive design
@media (max-width: 1200px) {
  .dashboard-container {
    .charts-section {
      grid-template-columns: 1fr;
    }
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 1rem;

    .page-header {
      .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;

        .header-actions {
          width: 100%;
          justify-content: space-between;

          .date-range {
            display: none;
          }
        }
      }
    }

    .stats-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .charts-section {
      gap: 1rem;
    }

    .transactions-card {
      .transactions-table {
        font-size: 0.875rem;

        .user-info {
          .user-name {
            display: none;
          }
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .dashboard-container {
    padding: 0.75rem;

    .page-header {
      margin-bottom: 1.5rem;

      .header-content {
        .header-text {
          .page-title {
            font-size: 1.5rem;
          }
        }

        .header-actions {
          flex-direction: column;
          align-items: stretch;
          gap: 0.75rem;

          .period-dropdown {
            min-width: auto;
          }
        }
      }
    }

    .stats-grid {
      margin-bottom: 1.5rem;
      gap: 0.75rem;

      .stat-card {
        .stat-content {
          .stat-details {
            .stat-value {
              font-size: 1.5rem;
            }
          }
        }
      }
    }

    .charts-section {
      margin-bottom: 1.5rem;
      gap: 0.75rem;

      .chart-card {
        .chart-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 0.75rem;

          .chart-legend {
            gap: 0.75rem;
          }
        }
      }
    }

    .transactions-card {
      .transactions-table {
        .user-info {
          gap: 0.5rem;

          .user-avatar {
            width: 28px;
            height: 28px;
            font-size: 0.625rem;
          }
        }

        .coin-info {
          .coin-icon {
            width: 20px;
            height: 20px;
            font-size: 0.625rem;
          }
        }
      }
    }
  }
}

// Animation
.dashboard-container {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Card hover effects
.stat-card,
.chart-card,
.wallet-card,
.transactions-card {
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
}

// Loading states
.stats-grid {
  .loading-card {
    background: #f3f4f6;
    border-radius: 12px;
    height: 120px;
    animation: pulse 1.5s ease-in-out infinite;
  }
}

.charts-section {
  .loading-chart {
    background: #f3f4f6;
    border-radius: 12px;
    height: 400px;
    animation: pulse 1.5s ease-in-out infinite;
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

// Dark mode support
.app-dark {
  .dashboard-container {
    background: #111827;

    .stat-card,
    .chart-card,
    .wallet-card,
    .transactions-card {
      background: #1f2937;
      border: 1px solid #374151;

      &:hover {
        border-color: #4b5563;
      }
    }

    .page-header {
      .header-content {
        .header-text {
          .page-title {
            color: #f9fafb;
          }

          .page-subtitle {
            color: #9ca3af;
          }
        }

        .header-actions {
          .date-range {
            color: #9ca3af;
          }
        }
      }
    }

    .stat-card {
      .stat-content {
        .stat-details {
          .stat-value {
            color: #f9fafb;
          }

          .stat-label {
            color: #9ca3af;
          }
        }
      }
    }

    .chart-card,
    .wallet-card,
    .transactions-card {
      .chart-header,
      .wallet-header,
      .table-header {
        h3 {
          color: #f9fafb;
        }
      }
    }

    .wallet-card {
      .wallet-content {
        .wallet-item {
          border-bottom-color: #374151;

          .wallet-info {
            .wallet-currency {
              color: #f9fafb;
            }

            .wallet-percentage {
              color: #9ca3af;
            }
          }

          .wallet-amount {
            color: #f9fafb;
          }
        }
      }
    }

    .transactions-card {
      .transactions-table {
        .user-info {
          .user-name {
            color: #f9fafb;
          }
        }

        .amount {
          color: #f9fafb;
        }
      }
    }
  }
}
