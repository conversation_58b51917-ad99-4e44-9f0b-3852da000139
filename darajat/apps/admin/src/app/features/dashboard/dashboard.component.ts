import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';
import { ChartModule } from 'primeng/chart';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { ProgressBarModule } from 'primeng/progressbar';
import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';
import { InputTextModule } from 'primeng/inputtext';
import { BadgeModule } from 'primeng/badge';
import { AvatarModule } from 'primeng/avatar';
import { MenuModule } from 'primeng/menu';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { DividerModule } from 'primeng/divider';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    CardModule,
    ButtonModule,
    ChartModule,
    TableModule,
    TagModule,
    ProgressBarModule,
    DropdownModule,
    CalendarModule,
    InputTextModule,
    BadgeModule,
    AvatarModule,
    MenuModule,
    OverlayPanelModule,
    DividerModule
  ],
  template: `
    <div class="dashboard-container">
      <!-- Page Header -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-text">
            <h1 class="page-title">Overview</h1>
            <p class="page-subtitle">Welcome to PrimeNG</p>
          </div>
          <div class="header-actions">
            <p-dropdown
              [options]="timePeriods"
              [(ngModel)]="selectedPeriod"
              optionLabel="label"
              placeholder="Weekly"
              styleClass="period-dropdown">
            </p-dropdown>
            <p-button
              label="Download"
              icon="pi pi-download"
              styleClass="p-button-outlined download-btn">
            </p-button>
            <span class="date-range">06/11/2024 - 06/22/2024</span>
          </div>
        </div>
      </div>

      <!-- Stats Cards Grid -->
      <div class="stats-grid">
        <p-card *ngFor="let stat of statsData" class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <i [class]="'pi ' + stat.icon" [style.color]="stat.iconColor"></i>
            </div>
            <div class="stat-details">
              <h3 class="stat-value">{{ stat.value }}</h3>
              <p class="stat-label">{{ stat.title }}</p>
              <div class="stat-trend" *ngIf="stat.trend">
                <span [class]="'trend-value ' + (stat.trend.isPositive ? 'positive' : 'negative')">
                  {{ stat.trend.isPositive ? '+' : '-' }}{{ stat.trend.value }}{{ stat.trend.label }}
                </span>
              </div>
            </div>
          </div>
        </p-card>
      </div>

      <!-- Charts Section -->
      <div class="charts-section">
        <!-- Crypto Analytics Chart -->
        <p-card class="chart-card crypto-chart">
          <ng-template pTemplate="header">
            <div class="chart-header">
              <h3>Crypto Analytics</h3>
              <div class="chart-legend">
                <span class="legend-item personal">
                  <span class="legend-dot"></span>
                  Personal Wallet
                </span>
                <span class="legend-item corporate">
                  <span class="legend-dot"></span>
                  Corporate Wallet
                </span>
                <span class="legend-item investment">
                  <span class="legend-dot"></span>
                  Investment Wallet
                </span>
              </div>
            </div>
          </ng-template>
          <p-chart
            type="bar"
            [data]="cryptoChartData"
            [options]="cryptoChartOptions"
            height="300px">
          </p-chart>
        </p-card>

        <!-- My Wallet Card -->
        <p-card class="wallet-card">
          <ng-template pTemplate="header">
            <div class="wallet-header">
              <h3>My Wallet</h3>
              <p-button
                icon="pi pi-ellipsis-h"
                styleClass="p-button-text p-button-rounded">
              </p-button>
            </div>
          </ng-template>
          <div class="wallet-content">
            <div class="wallet-item" *ngFor="let wallet of walletData">
              <div class="wallet-info">
                <span class="wallet-currency">{{ wallet.currency }}</span>
                <span class="wallet-percentage">({{ wallet.percentage }}%)</span>
              </div>
              <div class="wallet-amount">{{ wallet.amount }}</div>
            </div>
          </div>
        </p-card>
      </div>

      <!-- Transactions Table -->
      <p-card class="transactions-card">
        <ng-template pTemplate="header">
          <div class="table-header">
            <h3>Transactions</h3>
            <p-button
              icon="pi pi-ellipsis-h"
              styleClass="p-button-text p-button-rounded">
            </p-button>
          </div>
        </ng-template>
        <p-table
          [value]="transactions"
          [paginator]="true"
          [rows]="5"
          styleClass="transactions-table">
          <ng-template pTemplate="header">
            <tr>
              <th>Id</th>
              <th>Name</th>
              <th>Coin</th>
              <th>Date</th>
              <th>Process</th>
              <th>Amount</th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-transaction>
            <tr>
              <td>{{ transaction.id }}</td>
              <td>
                <div class="user-info">
                  <p-avatar
                    [label]="transaction.userInitials"
                    styleClass="user-avatar"
                    [style]="{'background-color': transaction.avatarColor}">
                  </p-avatar>
                  <span class="user-name">{{ transaction.name }}</span>
                </div>
              </td>
              <td>
                <div class="coin-info">
                  <span class="coin-icon" [style.background-color]="transaction.coinColor">
                    {{ transaction.coinSymbol }}
                  </span>
                </div>
              </td>
              <td>{{ transaction.date }}</td>
              <td>
                <p-tag
                  [value]="transaction.process"
                  [severity]="transaction.processSeverity">
                </p-tag>
              </td>
              <td class="amount">{{ transaction.amount }}</td>
            </tr>
          </ng-template>
        </p-table>
      </p-card>
    </div>
  `,
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  selectedPeriod: any = null;
  chartsLoading = false;
  tableLoading = false;

  timePeriods = [
    { label: 'Weekly', value: 'weekly' },
    { label: 'Monthly', value: 'monthly' },
    { label: 'Yearly', value: 'yearly' }
  ];

  // Stats data
  statsData = [
    {
      title: 'Total Students',
      value: '30,000',
      icon: 'pi-users',
      iconColor: '#6366f1',
      trend: {
        value: 12,
        isPositive: true,
        label: '%'
      }
    },
    {
      title: 'Total Teachers',
      value: '25,000',
      icon: 'pi-user',
      iconColor: '#10b981',
      trend: {
        value: 3,
        isPositive: true,
        label: '%'
      }
    },
    {
      title: 'Active Classes',
      value: '20,000',
      icon: 'pi-book',
      iconColor: '#f59e0b',
      trend: {
        value: 8,
        isPositive: true,
        label: '%'
      }
    },
    {
      title: 'System Usage',
      value: '15,000',
      icon: 'pi-chart-line',
      iconColor: '#ef4444',
      trend: {
        value: 2,
        isPositive: false,
        label: '%'
      }
    }
  ];

  // Chart data
  cryptoChartData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    datasets: [
      {
        label: 'Personal Wallet',
        data: [30000, 25000, 20000, 15000, 10000, 5000, 8000, 12000, 18000, 22000, 28000, 32000],
        backgroundColor: '#6366f1',
        borderRadius: 4,
        barThickness: 20
      },
      {
        label: 'Corporate Wallet',
        data: [20000, 18000, 16000, 14000, 12000, 10000, 13000, 16000, 19000, 21000, 24000, 26000],
        backgroundColor: '#8b5cf6',
        borderRadius: 4,
        barThickness: 20
      },
      {
        label: 'Investment Wallet',
        data: [15000, 12000, 10000, 8000, 6000, 4000, 7000, 10000, 13000, 16000, 19000, 22000],
        backgroundColor: '#06b6d4',
        borderRadius: 4,
        barThickness: 20
      }
    ]
  };

  cryptoChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          color: '#6b7280'
        }
      },
      y: {
        grid: {
          color: '#f3f4f6'
        },
        ticks: {
          color: '#6b7280',
          callback: function(value: any) {
            return value / 1000 + 'k';
          }
        }
      }
    }
  };

  // Wallet data
  walletData = [
    { currency: 'BTC', percentage: '15%', amount: '27.215' },
    { currency: 'ETH', percentage: '5%', amount: '4.367' },
    { currency: 'GBP', percentage: '25%', amount: '£ 147,562.32' },
    { currency: 'EUR', percentage: '11%', amount: '€ 137,457.25' },
    { currency: 'USD', percentage: '20%', amount: '$ 133,364.12' },
    { currency: 'XAU', percentage: '29%', amount: '200 g' }
  ];

  // Transactions data
  transactions = [
    {
      id: '#1254',
      name: 'Amy Yelsner',
      userInitials: 'AY',
      avatarColor: '#6366f1',
      coinSymbol: '●',
      coinColor: '#1f2937',
      date: 'May 5th',
      process: 'Buy',
      processSeverity: 'success',
      amount: '3.005 BTC'
    },
    {
      id: '#2355',
      name: 'Anna Fali',
      userInitials: 'AF',
      avatarColor: '#f59e0b',
      coinSymbol: '●',
      coinColor: '#f59e0b',
      date: 'Mar 17th',
      process: 'Buy',
      processSeverity: 'success',
      amount: '0.050 ETH'
    },
    {
      id: '#1235',
      name: 'Stepen Shaw',
      userInitials: 'SS',
      avatarColor: '#1f2937',
      coinSymbol: '●',
      coinColor: '#1f2937',
      date: 'May 24th',
      process: 'Sell',
      processSeverity: 'danger',
      amount: '3.050 BTC'
    },
    {
      id: '#2355',
      name: 'Anna Fali',
      userInitials: 'AF',
      avatarColor: '#f59e0b',
      coinSymbol: '●',
      coinColor: '#f59e0b',
      date: 'Mar 17th',
      process: 'Sell',
      processSeverity: 'danger',
      amount: '0.050 ETH'
    },
    {
      id: '#2355',
      name: 'Anna Fali',
      userInitials: 'AF',
      avatarColor: '#f59e0b',
      coinSymbol: '●',
      coinColor: '#f59e0b',
      date: 'Mar 17th',
      process: 'Sell',
      processSeverity: 'danger',
      amount: '0.050 ETH'
    }
  ];

  ngOnInit(): void {
    this.loadDashboardData();
    this.selectedPeriod = this.timePeriods[0];
  }

  onPeriodChange(period: any): void {
    this.selectedPeriod = period;
    this.loadDashboardData();
  }

  onRefresh(): void {
    this.loadDashboardData();
  }

  private loadDashboardData(): void {
    this.chartsLoading = true;
    this.tableLoading = true;

    // Simulate API calls
    setTimeout(() => {
      this.chartsLoading = false;
      this.tableLoading = false;
    }, 1500);
  }

  onDownload(): void {
    console.log('Downloading data...');
    // Implement download logic
  }
}
