import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { TooltipModule } from 'primeng/tooltip';
import { BadgeModule } from 'primeng/badge';

export interface SidebarMenuItem {
  id: string;
  label: string;
  icon: string;
  route?: string;
  badge?: string | number;
  badgeClass?: string;
  children?: SidebarMenuItem[];
  action?: () => void;
}

@Component({
  selector: 'darajat-sidebar',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ButtonModule,
    TooltipModule,
    BadgeModule,
  ],
  template: `
    <aside class="modern-sidebar">
      <div class="sidebar-container">
        <!-- Navigation Items -->
        <nav class="sidebar-nav">
          <div class="nav-section">
            <div
              *ngFor="let item of menuItems; trackBy: trackByItemId"
              class="nav-item"
              [class.active]="item.id === activeItemId"
            >
              <a
                *ngIf="item.route"
                [routerLink]="item.route"
                class="nav-link"
                (click)="onItemClick(item)"
              >
                <div class="nav-icon-container">
                  <i [class]="'pi ' + item.icon" class="nav-icon"></i>
                </div>
                <span class="nav-label">{{ item.label }}</span>
                <p-badge
                  *ngIf="item.badge"
                  [value]="item.badge.toString()"
                  [styleClass]="item.badgeClass || 'badge-primary'"
                  class="nav-badge"
                ></p-badge>
              </a>

              <button
                *ngIf="!item.route"
                pButton
                type="button"
                class="nav-link nav-button"
                (click)="onItemClick(item)"
              >
                <div class="nav-icon-container">
                  <i [class]="'pi ' + item.icon" class="nav-icon"></i>
                </div>
                <span class="nav-label">{{ item.label }}</span>
                <p-badge
                  *ngIf="item.badge"
                  [value]="item.badge.toString()"
                  [styleClass]="item.badgeClass || 'badge-primary'"
                  class="nav-badge"
                ></p-badge>
              </button>
            </div>
          </div>
        </nav>
      </div>
    </aside>
  `,
  styleUrls: ['./sidebar.component.scss']
})
export class SidebarComponent {
  @Input() isExpanded = false;
  @Input() activeItemId = '';
  @Input() menuItems: SidebarMenuItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: 'pi-home',
      route: '/dashboard'
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: 'pi-chart-line',
      route: '/analytics'
    },
    {
      id: 'messages',
      label: 'Messages',
      icon: 'pi-envelope',
      route: '/messages',
      badge: 5,
      badgeClass: 'badge-danger'
    },
    {
      id: 'reports',
      label: 'Reports',
      icon: 'pi-file-pdf',
      route: '/reports'
    },
    {
      id: 'users',
      label: 'Users',
      icon: 'pi-users',
      route: '/users'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: 'pi-cog',
      route: '/settings'
    }
  ];

  @Output() toggle = new EventEmitter<boolean>();
  @Output() itemClick = new EventEmitter<SidebarMenuItem>();

  onToggle(): void {
    this.isExpanded = !this.isExpanded;
    this.toggle.emit(this.isExpanded);
  }

  onItemClick(item: SidebarMenuItem): void {
    this.activeItemId = item.id;
    this.itemClick.emit(item);
    
    if (item.action) {
      item.action();
    }
  }

  trackByItemId(index: number, item: SidebarMenuItem): string {
    return item.id;
  }
}
