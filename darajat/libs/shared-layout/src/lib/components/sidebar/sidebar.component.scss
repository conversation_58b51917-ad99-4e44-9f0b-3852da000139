@use '../../styles/variables.scss' as *;

.modern-sidebar {
  position: fixed;
  top: 64px; // Header height
  left: 0;
  bottom: 0;
  width: 80px;
  background: #ffffff;
  border-right: 1px solid #e5e7eb;
  z-index: 1000;
  transition: all 0.3s ease;

  .sidebar-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 1rem 0;

    .sidebar-nav {
      flex: 1;
      overflow-y: auto;

      .nav-section {
        padding: 0 0.5rem;

        .nav-item {
          margin-bottom: 0.5rem;

          &.active {
            .nav-link {
              background: #f0f9ff;
              border-color: var(--p-primary-color, #3b82f6);

              .nav-icon-container {
                background: var(--p-primary-color, #3b82f6);
                color: #ffffff;
              }

              .nav-label {
                color: var(--p-primary-color, #3b82f6);
                font-weight: 600;
              }
            }
          }

          .nav-link {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 0.5rem;
            text-decoration: none;
            border-radius: 8px;
            border: 1px solid transparent;
            transition: all 0.2s ease;
            color: #6b7280;
            background: transparent;
            width: 100%;
            cursor: pointer;

            &:hover {
              background: #f9fafb;
              border-color: #e5e7eb;

              .nav-icon-container {
                background: #f3f4f6;
                color: #374151;
              }

              .nav-label {
                color: #374151;
              }
            }

            .nav-icon-container {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 32px;
              height: 32px;
              border-radius: 6px;
              background: #f8fafc;
              transition: all 0.2s ease;

              .nav-icon {
                font-size: 1rem;
                color: inherit;
              }
            }

            .nav-label {
              font-size: 0.75rem;
              font-weight: 500;
              text-align: center;
              line-height: 1.2;
              transition: all 0.2s ease;
            }

            .nav-badge {
              position: absolute;
              top: 0.5rem;
              right: 0.5rem;
              min-width: 16px;
              height: 16px;
              font-size: 0.625rem;
            }
          }

          .nav-button {
            border: none;
            background: transparent;
          }
        }
      }
    }
  }
}

// Badge styles
:host ::ng-deep {
  .badge-primary {
    background: var(--p-primary-color, #3b82f6);
    color: #ffffff;
  }

  .badge-danger {
    background: #ef4444;
    color: #ffffff;
  }

  .badge-warning {
    background: #f59e0b;
    color: #ffffff;
  }

  .badge-success {
    background: #10b981;
    color: #ffffff;
  }

  .badge-info {
    background: #06b6d4;
    color: #ffffff;
  }
}

// Responsive design
@media (max-width: 768px) {
  .modern-sidebar {
    transform: translateX(-100%);
    width: 240px;
    z-index: 1050;

    &.mobile-open {
      transform: translateX(0);
    }
  }
}

// Dark mode support
.app-dark {
  .modern-sidebar {
    background: #1f2937;
    border-right-color: #374151;

    .sidebar-nav {
      .nav-section {
        .nav-item {
          &.active {
            .nav-link {
              background: rgba(59, 130, 246, 0.1);

              .nav-icon-container {
                background: var(--p-primary-color, #3b82f6);
                color: #ffffff;
              }

              .nav-label {
                color: var(--p-primary-color, #3b82f6);
              }
            }
          }

          .nav-link {
            color: #9ca3af;

            &:hover {
              background: #374151;
              border-color: #4b5563;

              .nav-icon-container {
                background: #4b5563;
                color: #f3f4f6;
              }

              .nav-label {
                color: #f3f4f6;
              }
            }

            .nav-icon-container {
              background: #374151;
            }
          }
        }
      }
    }
  }
}

// Scrollbar styling
.sidebar-nav {
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 2px;

    &:hover {
      background: #9ca3af;
    }
  }
}

.app-dark .sidebar-nav {
  &::-webkit-scrollbar-thumb {
    background: #4b5563;

    &:hover {
      background: #6b7280;
    }
  }
}
