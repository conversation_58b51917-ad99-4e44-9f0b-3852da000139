@use '../../styles/variables.scss' as *;

.modern-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-index-fixed);
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);

  .header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
    padding: 0 1.5rem;
    max-width: 100%;
  }

  .header-left {
    display: flex;
    align-items: center;
    min-width: 0;

    .brand-section {
      display: flex;
      align-items: center;
      gap: 0.75rem;

      .brand-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background: #f8fafc;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
      }

      .brand-info {
        display: flex;
        flex-direction: column;
        min-width: 0;

        .brand-title {
          font-size: 1.125rem;
          font-weight: 600;
          color: #1f2937;
          margin: 0;
          line-height: 1.2;
        }

        .brand-subtitle {
          font-size: 0.75rem;
          color: #6b7280;
          line-height: 1;
        }
      }
    }
  }

  .header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    max-width: 400px;
    margin: 0 2rem;

    .search-container {
      width: 100%;
      max-width: 300px;

      .search-field {
        width: 100%;

        .search-input {
          width: 100%;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          padding: 0.5rem 0.75rem 0.5rem 2.5rem;
          font-size: 0.875rem;
          background: #f9fafb;
          transition: all 0.2s ease;

          &:focus {
            background: #ffffff;
            border-color: var(--p-primary-color, #3b82f6);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          }

          &::placeholder {
            color: #9ca3af;
          }
        }
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 0.75rem;

    .notification-btn {
      position: relative;
      width: 40px;
      height: 40px;
      border-radius: 8px;
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      color: #6b7280;
      transition: all 0.2s ease;

      &:hover {
        background: #f1f5f9;
        border-color: #cbd5e1;
        color: #374151;
      }

      .notification-badge {
        position: absolute;
        top: -2px;
        right: -2px;
        min-width: 18px;
        height: 18px;
        font-size: 0.75rem;
        border: 2px solid #ffffff;
      }
    }

    .profile-section {
      .user-avatar {
        width: 40px;
        height: 40px;
        cursor: pointer;
        border: 2px solid #e2e8f0;
        transition: all 0.2s ease;

        &:hover {
          border-color: var(--p-primary-color, #3b82f6);
          transform: scale(1.05);
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .modern-header {
    .header-container {
      padding: 0 1rem;
    }

    .header-center {
      margin: 0 1rem;
      max-width: 200px;

      .search-container {
        .search-field {
          .search-input {
            font-size: 0.8rem;
            padding: 0.4rem 0.6rem 0.4rem 2rem;
          }
        }
      }
    }

    .header-left {
      .brand-section {
        .brand-info {
          .brand-title {
            font-size: 1rem;
          }

          .brand-subtitle {
            font-size: 0.7rem;
          }
        }
      }
    }
  }
}

@media (max-width: 640px) {
  .modern-header {
    .header-container {
      padding: 0 0.75rem;
    }

    .header-center {
      display: none;
    }

    .header-left {
      .brand-section {
        .brand-info {
          .brand-subtitle {
            display: none;
          }
        }
      }
    }

    .header-right {
      gap: 0.5rem;

      .notification-btn {
        width: 36px;
        height: 36px;
      }

      .profile-section {
        .user-avatar {
          width: 36px;
          height: 36px;
        }
      }
    }
  }
}

// Dark mode support
.app-dark {
  .modern-header {
    background: #1f2937;
    border-bottom-color: #374151;

    .header-left {
      .brand-section {
        .brand-logo {
          background: #374151;
          border-color: #4b5563;
        }

        .brand-info {
          .brand-title {
            color: #f9fafb;
          }

          .brand-subtitle {
            color: #9ca3af;
          }
        }
      }
    }

    .header-center {
      .search-container {
        .search-field {
          .search-input {
            background: #374151;
            border-color: #4b5563;
            color: #f9fafb;

            &:focus {
              background: #4b5563;
              border-color: var(--p-primary-color, #3b82f6);
            }

            &::placeholder {
              color: #6b7280;
            }
          }
        }
      }
    }

    .header-right {
      .notification-btn {
        background: #374151;
        border-color: #4b5563;
        color: #9ca3af;

        &:hover {
          background: #4b5563;
          border-color: #6b7280;
          color: #f3f4f6;
        }
      }

      .profile-section {
        .user-avatar {
          border-color: #4b5563;

          &:hover {
            border-color: var(--p-primary-color, #3b82f6);
          }
        }
      }
    }
  }
}
