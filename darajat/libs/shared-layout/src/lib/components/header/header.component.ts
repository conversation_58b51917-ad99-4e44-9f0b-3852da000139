import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { InputIconModule } from 'primeng/inputicon';
import { IconFieldModule } from 'primeng/iconfield';
import { BadgeModule } from 'primeng/badge';
import { AvatarModule } from 'primeng/avatar';
import { MenuModule } from 'primeng/menu';
import { MenuItem } from 'primeng/api';

@Component({
  selector: 'darajat-header',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    InputTextModule,
    InputIconModule,
    IconFieldModule,
    BadgeModule,
    AvatarModule,
    MenuModule,

  ],
  template: `
    <header class="modern-header">
      <div class="header-container">
        <!-- Left Section -->
        <div class="header-left">
          <div class="brand-section">
            <div class="brand-logo">
              <i class="pi pi-shield" style="font-size: 1.5rem; color: var(--p-primary-color);"></i>
            </div>
            <div class="brand-info">
              <h2 class="brand-title">{{ pageTitle }}</h2>
              <span class="brand-subtitle">{{ title }}</span>
            </div>
          </div>
        </div>

        <!-- Center Section -->
        <div class="header-center">
          <!-- Search -->
          <div class="search-container">
            <p-iconField iconPosition="left" class="search-field">
              <p-inputIcon styleClass="pi pi-search"></p-inputIcon>
              <input
                pInputText
                type="text"
                placeholder="Search"
                [(ngModel)]="searchQuery"
                (input)="onSearch($event)"
                class="search-input"
              />
            </p-iconField>
          </div>
        </div>

        <!-- Right Section -->
        <div class="header-right">
          <!-- Notifications -->
          <button
            pButton
            type="button"
            icon="pi pi-bell"
            class="p-button-text p-button-rounded notification-btn"
            (click)="onNotificationClick()"
            [attr.aria-label]="'Notifications'"
          >
            <p-badge
              *ngIf="notificationCount > 0"
              [value]="notificationCount.toString()"
              severity="danger"
              class="notification-badge"
            ></p-badge>
          </button>

          <!-- User Profile -->
          <div class="profile-section">
            <p-avatar
              [image]="userAvatar"
              [label]="userInitials"
              shape="circle"
              size="normal"
              class="user-avatar"
              (click)="toggleProfileMenu()"
            ></p-avatar>
          </div>
        </div>
      </div>
    </header>
  `,
  styleUrls: ['./header.component.scss']
})
export class HeaderComponent {
  @Input() title = 'Darajat';
  @Input() pageTitle = 'Dashboard';
  @Input() userName = 'John Doe';
  @Input() userAvatar = '';
  @Input() userInitials = 'JD';
  @Input() notificationCount = 0;
  @Input() searchQuery = '';

  @Output() menuToggle = new EventEmitter<void>();
  @Output() search = new EventEmitter<string>();
  @Output() notificationClick = new EventEmitter<void>();
  @Output() profileAction = new EventEmitter<string>();

  showProfileMenu = false;

  profileMenuItems: MenuItem[] = [
    {
      label: 'Profile',
      icon: 'pi pi-user',
      command: () => this.onProfileAction('profile')
    },
    {
      label: 'Settings',
      icon: 'pi pi-cog',
      command: () => this.onProfileAction('settings')
    },
    {
      separator: true
    },
    {
      label: 'Logout',
      icon: 'pi pi-sign-out',
      command: () => this.onProfileAction('logout')
    }
  ];

  onMenuToggle(): void {
    this.menuToggle.emit();
  }

  onSearch(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.search.emit(target.value);
  }

  onNotificationClick(): void {
    this.notificationClick.emit();
  }

  onProfileAction(action: string): void {
    this.profileAction.emit(action);
    this.showProfileMenu = false;
  }

  toggleProfileMenu(): void {
    this.showProfileMenu = !this.showProfileMenu;
  }
}
