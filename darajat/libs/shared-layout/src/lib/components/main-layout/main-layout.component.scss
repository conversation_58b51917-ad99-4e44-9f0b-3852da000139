@use '../../styles/variables.scss' as *;

.layout-container {
  min-height: 100vh;
  background: #f8fafc;
  position: relative;
}

.main-content {
  margin-top: 64px; // Header height
  margin-left: 80px; // Sidebar width
  min-height: calc(100vh - 64px);
  transition: margin-left 0.3s ease;
  background: #f8fafc;

  &.sidebar-expanded {
    margin-left: 80px; // Keep same width since sidebar doesn't expand
  }

  &.no-sidebar {
    margin-left: 0;
  }

  .content-wrapper {
    padding: 1.5rem;
    max-width: 100%;
    overflow-x: auto;
  }
}

.mobile-overlay {
  position: fixed;
  top: 64px;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1040;
  backdrop-filter: blur(2px);
}

// Responsive adjustments
@media (max-width: 768px) {
  .main-content {
    margin-left: 0;

    &.sidebar-expanded {
      margin-left: 0;
    }

    .content-wrapper {
      padding: 1rem;
    }
  }
}

// Dark mode support
.app-dark {
  .layout-container {
    background: #111827;
  }

  .main-content {
    background: #111827;
  }
}

// Responsive design
@media (max-width: $breakpoint-lg) {
  .main-content {
    margin-left: 0;

    &.sidebar-expanded {
      margin-left: 0;
    }
  }
}

@media (max-width: $breakpoint-md) {
  .main-content {
    .content-wrapper {
      padding: var(--spacing-md);
    }
  }
}

@media (max-width: $breakpoint-sm) {
  .main-content {
    .content-wrapper {
      padding: var(--spacing-sm);
    }
  }
}

// Content area styling
.content-wrapper {
  // Ensure content doesn't get too wide on large screens
  max-width: 1400px;
  margin: 0 auto;

  // Grid layout for dashboard content
  &.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
  }

  // Flex layout for forms and details
  &.flex-layout {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  // Two-column layout
  &.two-column {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: var(--spacing-lg);

    @media (max-width: $breakpoint-lg) {
      grid-template-columns: 1fr;
    }
  }

  // Three-column layout
  &.three-column {
    display: grid;
    grid-template-columns: 250px 1fr 300px;
    gap: var(--spacing-lg);

    @media (max-width: $breakpoint-xl) {
      grid-template-columns: 1fr 300px;
    }

    @media (max-width: $breakpoint-lg) {
      grid-template-columns: 1fr;
    }
  }
}

// Page header styling
.page-header {
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--medium-gray);

  .page-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
  }

  .page-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin: 0;
  }

  .page-actions {
    margin-top: var(--spacing-md);
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
  }
}

// Card layouts
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.card-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

// Content sections
.content-section {
  background: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--medium-gray);

    .section-title {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--text-primary);
      margin: 0;
    }

    .section-actions {
      display: flex;
      gap: var(--spacing-sm);
    }
  }

  .section-content {
    padding: 1rem 0;
  }
}

// Loading states
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: var(--text-secondary);
}

// Empty states
.empty-state {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--text-secondary);

  .empty-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
  }

  .empty-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--spacing-sm);
  }

  .empty-description {
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-lg);
  }
}

// Utility classes
.full-height {
  height: calc(100vh - var(--header-height) - (var(--content-padding) * 2));
}

.scrollable {
  overflow-y: auto;
  max-height: calc(100vh - var(--header-height) - (var(--content-padding) * 2));
}

.sticky-top {
  position: sticky;
  top: var(--spacing-lg);
}

// Print styles
@media print {
  .layout-container {
    .main-content {
      margin-left: 0;
      margin-top: 0;

      .content-wrapper {
        padding: 0;
      }
    }
  }
}
