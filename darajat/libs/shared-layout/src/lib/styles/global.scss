// Import PrimeNG base theme
@import 'primeng/resources/themes/lara-light-blue/theme.css';
@import 'primeng/resources/primeng.min.css';
@import 'primeicons/primeicons.css';
@import 'primeflex/primeflex.css';

// Import our custom variables
@import './variables.scss';

// Import our custom VSCode blue themes
@import '../../../ui-components/src/lib/themes/vscode-blue-theme.scss';
@import '../../../ui-components/src/lib/themes/vscode-blue-dark-theme.scss';

// Global reset and base styles
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  color: var(--text-primary);
  background-color: var(--light-gray);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// Typography
h1, h2, h3, h4, h5, h6 {
  margin: 0 0 1rem 0;
  font-weight: var(--font-weight-semibold);
  line-height: 1.2;
  color: var(--text-primary);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

p {
  margin: 0 0 1rem 0;
  line-height: 1.6;
}

a {
  color: var(--primary-blue);
  text-decoration: none;
  transition: var(--transition-base);

  &:hover {
    color: var(--secondary-blue);
    text-decoration: underline;
  }

  &:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
  }
}

// Lists
ul, ol {
  margin: 0 0 1rem 0;
  padding-left: 1.5rem;
}

li {
  margin-bottom: 0.25rem;
}

// Code
code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
  background: var(--light-gray);
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
  color: var(--primary-blue);
}

pre {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: var(--light-gray);
  padding: 1rem;
  border-radius: var(--border-radius);
  overflow-x: auto;
  margin: 0 0 1rem 0;

  code {
    background: none;
    padding: 0;
  }
}

// Tables
table {
  width: 100%;
  border-collapse: collapse;
  margin: 0 0 1rem 0;
}

th, td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--medium-gray);
}

th {
  font-weight: var(--font-weight-semibold);
  background: var(--light-gray);
  color: var(--text-primary);
}

// Forms
input, textarea, select {
  font-family: inherit;
  font-size: inherit;
}

// Utility classes
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-success { color: var(--success); }
.text-warning { color: var(--warning); }
.text-error { color: var(--error); }
.text-info { color: var(--info); }

.bg-primary { background-color: var(--primary-blue); }
.bg-secondary { background-color: var(--secondary-blue); }
.bg-light { background-color: var(--light-gray); }
.bg-white { background-color: var(--white); }
.bg-success { background-color: var(--success); }
.bg-warning { background-color: var(--warning); }
.bg-error { background-color: var(--error); }
.bg-info { background-color: var(--info); }

.font-weight-normal { font-weight: var(--font-weight-normal); }
.font-weight-medium { font-weight: var(--font-weight-medium); }
.font-weight-semibold { font-weight: var(--font-weight-semibold); }
.font-weight-bold { font-weight: var(--font-weight-bold); }

.font-size-xs { font-size: var(--font-size-xs); }
.font-size-sm { font-size: var(--font-size-sm); }
.font-size-base { font-size: var(--font-size-base); }
.font-size-lg { font-size: var(--font-size-lg); }
.font-size-xl { font-size: var(--font-size-xl); }
.font-size-2xl { font-size: var(--font-size-2xl); }
.font-size-3xl { font-size: var(--font-size-3xl); }

// Spacing utilities
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-xs); }
.m-2 { margin: var(--spacing-sm); }
.m-3 { margin: var(--spacing-md); }
.m-4 { margin: var(--spacing-lg); }
.m-5 { margin: var(--spacing-xl); }
.m-6 { margin: var(--spacing-2xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }
.p-6 { padding: var(--spacing-2xl); }

// Margin and padding directional utilities
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: var(--spacing-xs); }
.ml-2 { margin-left: var(--spacing-sm); }
.ml-3 { margin-left: var(--spacing-md); }
.ml-4 { margin-left: var(--spacing-lg); }
.ml-5 { margin-left: var(--spacing-xl); }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: var(--spacing-xs); }
.mr-2 { margin-right: var(--spacing-sm); }
.mr-3 { margin-right: var(--spacing-md); }
.mr-4 { margin-right: var(--spacing-lg); }
.mr-5 { margin-right: var(--spacing-xl); }

// Display utilities
.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

// Flex utilities
.flex-row { flex-direction: row; }
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }
.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.align-start { align-items: flex-start; }
.align-center { align-items: center; }
.align-end { align-items: flex-end; }
.align-stretch { align-items: stretch; }

// Border utilities
.border { border: 1px solid var(--medium-gray); }
.border-top { border-top: 1px solid var(--medium-gray); }
.border-bottom { border-bottom: 1px solid var(--medium-gray); }
.border-left { border-left: 1px solid var(--medium-gray); }
.border-right { border-right: 1px solid var(--medium-gray); }
.border-0 { border: none; }

.rounded { border-radius: var(--border-radius); }
.rounded-sm { border-radius: 3px; }
.rounded-lg { border-radius: 12px; }
.rounded-full { border-radius: 50%; }

// Shadow utilities
.shadow { box-shadow: var(--box-shadow); }
.shadow-lg { box-shadow: var(--box-shadow-elevated); }
.shadow-none { box-shadow: none; }

// Position utilities
.position-relative { position: relative; }
.position-absolute { position: absolute; }
.position-fixed { position: fixed; }
.position-sticky { position: sticky; }

// Overflow utilities
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }

// Width and height utilities
.w-100 { width: 100%; }
.h-100 { height: 100%; }
.w-auto { width: auto; }
.h-auto { height: auto; }

// Visibility utilities
.visible { visibility: visible; }
.invisible { visibility: hidden; }

// Screen reader only
.sr-only {
  @include visually-hidden;
}

// Responsive utilities
@media (max-width: $breakpoint-sm) {
  .d-sm-none { display: none; }
  .d-sm-block { display: block; }
  .d-sm-flex { display: flex; }
}

@media (max-width: $breakpoint-md) {
  .d-md-none { display: none; }
  .d-md-block { display: block; }
  .d-md-flex { display: flex; }
}

@media (max-width: $breakpoint-lg) {
  .d-lg-none { display: none; }
  .d-lg-block { display: block; }
  .d-lg-flex { display: flex; }
}
