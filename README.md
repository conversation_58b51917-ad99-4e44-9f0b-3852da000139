# Darajat - School Management System

A modern, multi-application school management system built with Angular 18, Nx monorepo, and PrimeNG v20.

## 🏗️ Project Structure

This is an Nx monorepo containing multiple Angular applications and shared libraries:

```
darajat/
├── apps/
│   ├── admin/          # Admin dashboard application
│   ├── students/       # Student portal application
│   ├── parents/        # Parent portal application
│   ├── professor/      # Teacher/Professor application
│   └── school/         # School management application
├── libs/
│   ├── shared-layout/  # Shared layout components (header, sidebar, etc.)
│   ├── shared-services/# Shared services (theme, auth, etc.)
│   ├── shared-utils/   # Shared utilities and helpers
│   └── ui-components/  # Shared UI components and PrimeNG modules
└── dist/              # Build output directory
```

## 🚀 Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Git

### Installation

1. **Clone the repository:**
   ```bash
   git clone  https://github.com/mhdabdelkader2000/Darajat_Web_School.git
   cd darajat
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Set up environment (if using nvm):**
   ```bash
   export NVM_DIR="$HOME/.nvm" && [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
   ```

## 🛠️ Development Commands

### Running Applications

Each application can be served independently:

```bash
# Admin Dashboard (recommended port: 4200)
nx serve admin --port 4200

# Student Portal
nx serve students --port 4201

# Parent Portal
nx serve parents --port 4202

# Professor Portal
nx serve professor --port 4203

# School Management
nx serve school --port 4204
```

### Quick Start (Admin Dashboard)

```bash
# Reset cache and start admin app
export NVM_DIR="$HOME/.nvm" && [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh" && npx nx reset && nx serve admin --port 4200
```

### Building Applications

```bash
# Build specific application
nx build admin
nx build students
nx build parents
nx build professor
nx build school

# Build all applications
nx run-many --target=build --all
```

### Testing

```bash
# Run tests for specific app/lib
nx test admin
nx test shared-layout

# Run all tests
nx run-many --target=test --all

# Run tests with coverage
nx test admin --coverage
```

### Linting

```bash
# Lint specific app/lib
nx lint admin
nx lint shared-layout

# Lint all projects
nx run-many --target=lint --all
```

## 🎨 UI Framework & Theming

### PrimeNG v20

This project uses **PrimeNG v20** with the new design token system:

- **No more CSS imports** - Themes are configured via TypeScript
- **Design tokens** - Customizable color schemes and component styles
- **Modern components** - Latest PrimeNG components with improved accessibility

### Theme Configuration

Themes are configured in each app's `app.config.ts`:

```typescript
import { providePrimeNG } from 'primeng/config';

export const appConfig: ApplicationConfig = {
  providers: [
    // ... other providers
    providePrimeNG({
      theme: {
        preset: basicTheme, // Theme configuration
        options: {
          prefix: 'p',
          darkModeSelector: '.app-dark',
          cssLayer: false
        }
      }
    }),
  ],
};
```

## 📁 Project Architecture

### Applications

- **Admin** (`apps/admin/`) - Complete school administration dashboard
- **Students** (`apps/students/`) - Student portal for grades, assignments, etc.
- **Parents** (`apps/parents/`) - Parent portal to track children's progress
- **Professor** (`apps/professor/`) - Teacher interface for managing classes
- **School** (`apps/school/`) - School-wide management and settings

### Shared Libraries

- **shared-layout** - Reusable layout components (header, sidebar, main layout)
- **shared-services** - Common services (authentication, theme management, etc.)
- **shared-utils** - Utility functions and helpers
- **ui-components** - PrimeNG module exports and custom components

## 🔧 Nx Commands Reference

### Project Management

```bash
# Generate new application
nx g @nx/angular:application my-app

# Generate new library
nx g @nx/angular:library my-lib

# Generate component
nx g @nx/angular:component my-component --project=admin

# Generate service
nx g @nx/angular:service my-service --project=shared-services
```

### Dependency Management

```bash
# Show project dependencies
nx graph

# Show what's affected by changes
nx affected:graph

# Run commands only on affected projects
nx affected:test
nx affected:build
nx affected:lint
```

### Cache Management

```bash
# Reset Nx cache
nx reset

# Clear specific cache
nx reset --cache
```

## 🌐 Development Workflow

### 1. Start Development Server

```bash
# Reset cache and start admin app
export NVM_DIR="$HOME/.nvm" && [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh" && npx nx reset && nx serve admin --port 4200
```

### 2. Making Changes

- **Components**: Add to appropriate app or shared library
- **Services**: Usually go in `shared-services`
- **UI Components**: Add to `ui-components` if reusable
- **Layouts**: Modify `shared-layout` for global changes

### 3. Testing Changes

```bash
# Test specific project
nx test admin

# Test affected projects
nx affected:test
```

## 🎯 Key Features

### Modern Layout System

- **Responsive design** - Works on desktop, tablet, and mobile
- **Modern sidebar** - Icon-based navigation with labels
- **Clean header** - Search, notifications, and user profile
- **Card-based content** - Modern, clean content areas

### Theme System

- **Dynamic theming** - Switch themes at runtime
- **Dark mode support** - Automatic and manual dark mode
- **Customizable** - Easy to modify colors and styles

### Shared Components

- **Consistent UI** - Shared components across all apps
- **PrimeNG integration** - Full PrimeNG component library
- **Type safety** - Full TypeScript support

## 🚨 Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   # Use different port
   nx serve admin --port 4201
   ```

2. **Build errors after changes**
   ```bash
   # Reset cache and rebuild
   nx reset && nx build admin
   ```

3. **Sass deprecation warnings**
   - These are just warnings and don't affect functionality
   - Will be addressed in future updates

### Getting Help

- Check the [Nx documentation](https://nx.dev)
- Check the [PrimeNG documentation](https://primeng.org)
- Check the [Angular documentation](https://angular.io)

## 📝 Development Notes

- Always run `nx reset` if you encounter strange build issues
- Use the shared libraries for common functionality
- Follow the established patterns for consistency
- Test your changes across different screen sizes
- Use the theme service for dynamic theming

## 🔄 Next Steps

1. **Install @primeuix/themes** package for better theme support
2. **Add more dashboard widgets** to the admin interface
3. **Implement authentication** across all applications
4. **Add data services** for real backend integration
5. **Enhance responsive design** for mobile devices
